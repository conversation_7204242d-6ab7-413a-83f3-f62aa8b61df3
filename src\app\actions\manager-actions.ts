"use server";
import { createAction } from "@/lib/actions/core";
import { ApiResponse } from "@/lib/actions/types";
import { getSupabaseAdmin } from "@/utils/supabase/admin";
import { createClient } from "@/utils/supabase/server";
import { logSuccess, logError } from "@/lib/audit-logger";
import { createNotification } from "@/lib/actions/notifications";

import { revalidatePath } from "next/cache";

// Güvenlik kontrol sonucu tipi
interface SecurityCheckResult {
  success: boolean;
  error?: string;
  userId?: string;
  userProfile?: any;
  managerGyms?: any[];
}

// Reports için tip tanımları
export interface ReportData {
  totalRevenue: number;
  totalMembers: number;
  totalGyms: number;
  monthlyGrowth: number;
  revenueGrowth: number;
  memberGrowth: number;
  topPerformingGyms: {
    id: string;
    name: string;
    slug: string;
    revenue: number;
    members: number;
    growth: number;
  }[];
  monthlyData: {
    month: string;
    revenue: number;
    members: number;
  }[];
}

export interface ReportFilters {
  period: "month" | "quarter" | "year";
  startDate?: string;
  endDate?: string;
}

/**
 * Yönetici yetkilerini ve salon erişimini kontrol eden merkezi güvenlik fonksiyonu
 * @param gymId Kontrol edilecek salon ID'si (opsiyonel)
 * @returns Güvenlik kontrol sonucu
 */
export async function checkManagerAccess(
  gymId?: string
): Promise<SecurityCheckResult> {
  try {
    const supabase = await createClient();

    // 1. Kullanıcının oturum açıp açmadığını kontrol et
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData?.user) {
      return {
        success: false,
        error: "Oturum açmanız gerekiyor.",
      };
    }

    // 2. Kullanıcı profilini al
    const { data: profileData, error: profileError } = await supabase
      .from("users")
      .select("*")
      .eq("id", userData.user.id)
      .single();

    if (profileError || !profileData) {
      return {
        success: false,
        error: "Kullanıcı profili bulunamadı.",
      };
    }

    // 3. Yönetici yetkisini kontrol et
    if (!profileData.is_manager) {
      return {
        success: false,
        error:
          "Bu işleme erişim izniniz bulunmamaktadır. Sadece salon yöneticileri bu işlemi yapabilir.",
      };
    }

    // 4. Yöneticinin sahip olduğu salonları al
    const { data: managerGyms, error: managerGymsError } = await supabase
      .from("gyms")
      .select("id, name, manager_user_id")
      .eq("manager_user_id", profileData.id);

    if (managerGymsError) {
      return {
        success: false,
        error: "Salon bilgileri alınırken hata oluştu.",
      };
    }

    // 5. Eğer belirli bir salon ID'si verilmişse, erişim kontrolü yap
    if (gymId) {
      const hasGymAccess = managerGyms?.some((gym: any) => gym.id === gymId);

      if (!hasGymAccess) {
        return {
          success: false,
          error: "Bu salona erişim izniniz bulunmamaktadır.",
        };
      }
    }

    // 6. Yöneticinin en az bir salonu olup olmadığını kontrol et
    if (!managerGyms || managerGyms.length === 0) {
      return {
        success: false,
        error: "Henüz hiçbir salonunuz bulunmamaktadır.",
      };
    }

    return {
      success: true,
      userId: userData.user.id,
      userProfile: profileData,
      managerGyms: managerGyms,
    };
  } catch (error: any) {
    console.error("Güvenlik kontrolü sırasında hata:", error);
    return {
      success: false,
      error: "Güvenlik kontrolü sırasında bir hata oluştu.",
    };
  }
}

/**
 * Salon ayarlarını günceller
 * @param gymId Salon ID'si
 * @param gymSettings Salon ayarları
 * @returns Güncellenmiş salon bilgileri veya hata
 */
export async function updateGymSettings(gymId: string, gymSettings: any) {
  try {
    // Güvenlik kontrolü - yönetici yetkisi ve salon erişimi
    const securityCheck = await checkManagerAccess(gymId);
    if (!securityCheck.success) {
      return { error: securityCheck.error };
    }

    const supabase = await createClient();

    // Güvenlik: Sadece belirli alanların güncellenmesine izin ver
    const allowedFields = [
      "name",
      "description",
      "address",
      "phone",
      "email",
      "opening_hours",
      "facilities",
      "pricing",
      "rules",
    ];

    const sanitizedSettings = Object.keys(gymSettings)
      .filter((key) => allowedFields.includes(key))
      .reduce((obj: any, key) => {
        obj[key] = gymSettings[key];
        return obj;
      }, {});

    // Güncelleme zamanını ekle
    sanitizedSettings.updated_at = new Date().toISOString();

    // Mevcut salon bilgilerini al (audit log için)
    const { data: currentGym } = await supabase
      .from("gyms")
      .select("*")
      .eq("id", gymId)
      .maybeSingle();

    const { data, error } = await supabase
      .from("gyms")
      .update(sanitizedSettings)
      .eq("id", gymId)
      .select()
      .maybeSingle();

    if (error) {
      console.error("Salon ayarları güncellenirken hata oluştu:", error);

      // Hata audit log
      await logError(
        "update_gym_settings",
        "gym",
        `Salon ayarları güncellenirken hata oluştu: ${error.message}`,
        error.message,
        {
          resourceId: gymId,
          gymId: gymId,
          metadata: {
            attempted_changes: sanitizedSettings,
            error_code: error.code,
          },
        }
      );

      return { error: error.message };
    }

    // Başarı audit log
    await logSuccess(
      "update_gym_settings",
      "gym",
      `Salon ayarları güncellendi: ${data?.name || "Salon"}`,
      {
        resourceId: gymId,
        gymId: gymId,
        oldValues: currentGym
          ? {
              name: currentGym.name,
              description: currentGym.description,
              address: currentGym.address,
              phone: currentGym.phone,
              email: currentGym.email,
            }
          : undefined,
        newValues: sanitizedSettings,
        metadata: {
          updated_fields: Object.keys(sanitizedSettings),
          update_count: Object.keys(sanitizedSettings).length,
        },
      }
    );

    // Salon ayarları sayfasını yeniden doğrula
    revalidatePath("/settings/gym-settings");
    return { success: true, data };
  } catch (error: any) {
    console.error("Salon ayarları güncellenirken beklenmeyen hata:", error);
    return {
      error:
        "Salon ayarları güncellenirken bir hata oluştu. Lütfen tekrar deneyin.",
    };
  }
}

/**
 * Salon yöneticisinin yeni bir üye oluşturması için server action
 * @param userData Oluşturulacak üyenin bilgileri
 * @returns Oluşturulan üye veya hata
 */
/**
 * Salon yöneticisinin yeni bir üye oluşturması için server action
 * @param newUserData Oluşturulacak üyenin bilgileri ve salon ID'si
 * @returns Oluşturulan üye veya hata
 */
export async function createMemberByManager(newUserData: {
  email: string;
  name: string;
  surname: string;
  gender: string;
  gymId: string;
}) {
  try {
    // Güvenlik kontrolü - yönetici yetkisi ve salon erişimi
    const securityCheck = await checkManagerAccess(newUserData.gymId);
    if (!securityCheck.success) {
      return { error: securityCheck.error };
    }

    // Input validasyonu
    if (
      !newUserData.email ||
      !newUserData.name ||
      !newUserData.surname ||
      !newUserData.gymId
    ) {
      return { error: "Tüm zorunlu alanları doldurunuz." };
    }

    // Email formatı kontrolü
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newUserData.email)) {
      return { error: "Geçerli bir e-posta adresi giriniz." };
    }

    const adminClient = getSupabaseAdmin();
    // Önce bu e-posta adresiyle kullanıcı var mı kontrol et
    const { data: existingUser, error: findError } = await adminClient
      .from("users")
      .select("id, email, name, surname")
      .eq("email", newUserData.email)
      .maybeSingle();

    if (findError && findError.code !== "PGRST116") {
      console.error("Kullanıcı aranırken hata:", findError);
      return { error: findError.message };
    }

    // Salon varlığını kontrol et
    const { data: gymData, error: gymError } = await adminClient
      .from("gyms")
      .select("id, name, manager_user_id")
      .eq("id", newUserData.gymId)
      .maybeSingle();

    if (gymError) {
      console.error("Salon bilgisi alınırken hata:", gymError);
      return {
        error: "Salon bilgisi bulunamadı. Seçtiğiniz salon geçerli değil.",
      };
    }

    let userId: string;

    // Eğer kullanıcı zaten varsa
    if (existingUser) {
      userId = existingUser.id;

      // Kullanıcı bilgilerini güncelle
      const { error: updateError } = await adminClient
        .from("users")
        .update({
          name: newUserData.name,
          surname: newUserData.surname,
          gender: newUserData.gender,
          updated_at: new Date().toISOString(),
        })
        .eq("id", userId);

      if (updateError) {
        console.error("Kullanıcı güncellenirken hata:", updateError);
        return { error: updateError.message };
      }
    }
    // Yeni kullanıcı oluştur
    else {
      // 1. Auth sisteminde kullanıcı oluştur (admin API kullanarak)
      const { data: authData, error: authError } =
        await adminClient.auth.admin.createUser({
          email: newUserData.email,
          email_confirm: true, // E-postayı doğrulanmış olarak işaretle
          user_metadata: {
            name: newUserData.name,
            surname: newUserData.surname,
            gender: newUserData.gender,
          },
          password: `Sp${Math.random()
            .toString(36)
            .substring(2, 8)}${Math.floor(Math.random() * 1000)}!`, // Güvenli rastgele şifre
        });

      if (authError) {
        console.error("Auth kullanıcısı oluşturulurken hata:", authError);
        return { error: authError.message };
      }

      if (!authData.user) {
        return { error: "Kullanıcı oluşturma başarısız oldu." };
      }

      userId = authData.user.id;

      // 2. Public.users tablosunda profil oluştur
      const { error: insertError } = await adminClient.from("users").insert({
        id: userId,
        email: newUserData.email,
        name: newUserData.name,
        surname: newUserData.surname,
        gender: newUserData.gender,
        is_manager: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      if (insertError) {
        console.error("Kullanıcı profili oluşturulurken hata:", insertError);
        return { error: insertError.message };
      }

      // 3. Şifre sıfırlama bağlantısı gönder
      const { error: resetError } =
        await adminClient.auth.resetPasswordForEmail(newUserData.email, {
          redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?from=invitation`,
        });

      if (resetError) {
        console.error(
          "Şifre sıfırlama e-postası gönderilirken hata:",
          resetError
        );
        // Bu hata kritik değil, kullanıcı oluşturuldu ve üyelik eklenebilir
      }
    }

    // Şimdi üyelik oluştur
    const { data: membership, error: membershipError } = await adminClient
      .from("memberships")
      .insert({
        user_id: userId,
        gym_id: newUserData.gymId, // Client'dan gelen salon ID'si
        status: "approved_passive",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .maybeSingle();

    if (membershipError) {
      console.error("Üyelik oluşturulurken hata:", membershipError);
      return { error: membershipError.message };
    }

    // Kullanıcıya bildirim ekle
    try {
      await createNotification({
        userId: userId,
        title: "Yeni Salon Üyeliği",
        message: `${gymData?.name} salonuna üye olarak eklendiniz.`,
        type: "membership_approved",
        relatedEntityType: "gyms",
        relatedEntityId: newUserData.gymId,
      });
    } catch (notifError) {
      console.error("Bildirim eklenirken hata:", notifError);
      // Bildirim hatası kritik değil, devam edebiliriz
    }

    // Başarı audit log
    await logSuccess(
      "create_member",
      "member",
      `Yeni üye oluşturuldu: ${newUserData.name} ${newUserData.surname} (${newUserData.email})`,
      {
        resourceId: userId,
        gymId: newUserData.gymId,
        newValues: {
          email: newUserData.email,
          name: newUserData.name,
          surname: newUserData.surname,
          gender: newUserData.gender,
          membership_status: "approved_passive",
          is_new_user: !existingUser,
        },
        metadata: {
          membership_id: membership?.id,
          password_reset_sent: !existingUser,
          user_existed: !!existingUser,
        },
      }
    );

    // Üye yönetim sayfasını yenile
    revalidatePath("/dashboard/manager/members");

    return {
      success: true,
      userId,
      membership,
      passwordResetSent: !existingUser, // Yeni kullanıcı oluşturulduysa şifre sıfırlama e-postası gönderildi
    };
  } catch (error: any) {
    console.error("Üye oluşturulurken beklenmeyen hata:", error);

    // Hata audit log
    await logError(
      "create_member",
      "member",
      `Üye oluşturulurken hata: ${newUserData.name} ${newUserData.surname} (${newUserData.email})`,
      error.message,
      {
        gymId: newUserData.gymId,
        metadata: {
          attempted_user_data: {
            email: newUserData.email,
            name: newUserData.name,
            surname: newUserData.surname,
            gender: newUserData.gender,
          },
          error_type: "unexpected_error",
        },
      }
    );

    return {
      error: "Üye oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.",
    };
  }
}

/**
 * Bir üyeyi salona davet etmek için server action
 * @param userId Davet edilecek kullanıcı ID'si
 * @param gymId Salon ID'si
 * @returns Oluşturulan üyelik isteği veya hata
 */
/**
 * Bir mevcut kullanıcıyı salona davet etmek için server action
 * @param memberUserId Davet edilecek kullanıcı ID'si
 * @param gymId Salon ID'si (localStorage'dan client'da alınıp gönderiliyor)
 * @returns Oluşturulan üyelik veya hata
 */
export async function inviteMemberToGym(memberUserId: string, gymId: string) {
  try {
    // Güvenlik kontrolü - yönetici yetkisi ve salon erişimi
    const securityCheck = await checkManagerAccess(gymId);
    if (!securityCheck.success) {
      return { error: securityCheck.error };
    }

    // Input validasyonu
    if (!memberUserId || !gymId) {
      return { error: "Kullanıcı ID'si ve salon ID'si gereklidir." };
    }

    const supabase = await createClient();

    // Kullanıcının varlığını kontrol et
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, email, name, surname")
      .eq("id", memberUserId)
      .maybeSingle();

    if (userError) {
      console.error("Kullanıcı kontrolü sırasında hata:", userError);
      return { error: userError.message };
    }

    if (!userData) {
      return { error: "Davet edilecek kullanıcı bulunamadı." };
    }

    // Salon bilgisini al (bildirim için gerekli)
    const { data: gymData, error: gymError } = await supabase
      .from("gyms")
      .select("id, name")
      .eq("id", gymId)
      .maybeSingle();

    if (gymError || !gymData) {
      console.error("Salon bilgisi alınırken hata:", gymError);
      return {
        error: "Salon bilgisi bulunamadı.",
      };
    }

    // Aynı salon için daha önce bir üyelik var mı kontrol et
    const { data: existingMembership, error: checkError } = await supabase
      .from("memberships")
      .select("id, status")
      .eq("user_id", memberUserId)
      .eq("gym_id", gymId)
      .maybeSingle();

    if (checkError && checkError.code !== "PGRST116") {
      console.error("Üyelik kontrolü sırasında hata:", checkError);
      return { error: checkError.message };
    }

    // Eğer zaten bir üyelik varsa, tekrar oluşturma
    if (existingMembership) {
      return {
        error: "Bu kullanıcı zaten bu salona üye veya davet edilmiş durumda.",
        membership: existingMembership,
      };
    }

    // Yönetici tarafından davet edildiği için doğrudan approved_passive olarak ekleyelim
    const { data: membership, error: membershipError } = await supabase
      .from("memberships")
      .insert({
        user_id: memberUserId,
        gym_id: gymId,
        status: "approved_passive", // Yönetici tarafından eklendiği için doğrudan onaylı yapıyoruz
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .maybeSingle();

    if (membershipError) {
      console.error("Üyelik daveti oluşturulurken hata:", membershipError);
      return { error: membershipError.message };
    }

    // Kullanıcıya bildirim ekle
    try {
      await createNotification({
        userId: memberUserId,
        title: "Yeni Salon Üyeliği",
        message: `${gymData?.name} salonuna üye olarak eklendiniz.`,
        type: "membership_approved",
        relatedEntityType: "gyms",
        relatedEntityId: gymId,
      });
    } catch (notifError) {
      console.error("Bildirim eklenirken hata:", notifError);
      // Bildirim eklenememesi kritik bir hata değil, devam edebiliriz
    }

    // Başarı audit log
    await logSuccess(
      "invite_member",
      "membership",
      `Mevcut kullanıcı salona davet edildi: ${userData.name} ${userData.surname} (${userData.email})`,
      {
        resourceId: membership?.id,
        gymId: gymId,
        newValues: {
          user_id: memberUserId,
          gym_id: gymId,
          status: "approved_passive",
          user_email: userData.email,
          user_name: `${userData.name} ${userData.surname}`,
        },
        metadata: {
          membership_id: membership?.id,
          gym_name: gymData?.name,
          invitation_type: "existing_user",
        },
      }
    );

    // Üye yönetim sayfasını yenile
    revalidatePath("/dashboard/manager/members");

    return {
      success: true,
      membership,
    };
  } catch (error: any) {
    console.error("Üyelik daveti gönderilirken beklenmeyen hata:", error);

    // Hata audit log
    await logError(
      "invite_member",
      "membership",
      `Üyelik daveti gönderilirken hata: Kullanıcı ID ${memberUserId}`,
      error.message,
      {
        gymId: gymId,
        metadata: {
          member_user_id: memberUserId,
          error_type: "unexpected_error",
        },
      }
    );

    return {
      error:
        "Üyelik daveti gönderilirken bir hata oluştu. Lütfen tekrar deneyin.",
    };
  }
}

/**
 * Kullanıcı arama server action'ı
 * @param searchTerm Aranacak metin
 * @param excludeUserIds Aramadan hariç tutulacak kullanıcı ID'leri
 * @returns Eşleşen kullanıcı listesi veya hata
 */
export async function searchUsers(
  searchTerm: string,
  excludeUserIds: string[] = []
): Promise<ApiResponse> {
  // Güvenlik kontrolü - sadece yöneticiler kullanıcı arayabilir
  const securityCheck = await checkManagerAccess();
  if (!securityCheck.success) {
    return { success: false, error: securityCheck.error };
  }

  if (!searchTerm) {
    return { success: true, data: [] };
  }

  // Arama terimini sanitize et
  const sanitizedSearchTerm = searchTerm.trim().substring(0, 50);
  if (sanitizedSearchTerm.length < 2) {
    return { success: true, data: [] };
  }

  try {
    // Admin client kullan - RLS bypass için
    const adminClient = getSupabaseAdmin();

    // Kullanıcı arama sorgusu
    let query = adminClient
      .from("users")
      .select("id, email, name, surname, created_at") // Sadece gerekli alanları seç
      .or(
        `name.ilike.%${sanitizedSearchTerm}%,surname.ilike.%${sanitizedSearchTerm}%,email.ilike.%${sanitizedSearchTerm}%`
      )
      .limit(10);

    // Hariç tutulacak ID'ler varsa ekle
    if (excludeUserIds.length > 0) {
      query = query.not("id", "in", `(${excludeUserIds.join(",")})`);
    }

    const { data, error } = await query;

    if (error) {
      // Hata audit log
      await logError(
        "search_users",
        "user",
        `Kullanıcı arama hatası: ${error.message}`,
        error.message,
        {
          metadata: {
            search_term: sanitizedSearchTerm,
            exclude_user_ids: excludeUserIds,
            error_code: error.code,
          },
        }
      );

      return {
        success: false,
        error: `Kullanıcı arama hatası: ${error.message}`,
      };
    }

    // Başarı audit log (sadece arama yapıldığında)
    if (data && data.length > 0) {
      await logSuccess(
        "search_users",
        "user",
        `Kullanıcı araması yapıldı: "${sanitizedSearchTerm}" - ${data.length} sonuç bulundu`,
        {
          metadata: {
            search_term: sanitizedSearchTerm,
            result_count: data.length,
            exclude_user_ids: excludeUserIds,
          },
        }
      );
    }

    return { success: true, data: data || [] };
  } catch (error: any) {
    console.error("Search users error:", error);
    return { success: false, error: "Kullanıcı aranırken bir hata oluştu." };
  }
}

/**
 * Yöneticinin sahip olduğu salonları getirir
 * @returns Salon listesi veya hata
 */
export async function getManagerGyms() {
  try {
    const securityCheck = await checkManagerAccess();
    if (!securityCheck.success) {
      return { error: securityCheck.error };
    }

    return {
      success: true,
      data: securityCheck.managerGyms,
    };
  } catch (error: any) {
    console.error("Salon listesi alınırken hata:", error);
    return {
      error: "Salon listesi alınırken bir hata oluştu.",
    };
  }
}

/**
 * Belirli bir salona erişim yetkisi kontrolü
 * @param gymId Kontrol edilecek salon ID'si
 * @returns Erişim yetkisi var mı?
 */
export async function hasGymAccess(gymId: string): Promise<boolean> {
  try {
    const securityCheck = await checkManagerAccess(gymId);
    return securityCheck.success;
  } catch (error) {
    console.error("Salon erişim kontrolü sırasında hata:", error);
    return false;
  }
}

/**
 * Salon audit loglarını getirir (sadece yöneticiler için)
 * @param gymId Salon ID'si
 * @param filters Filtreleme seçenekleri
 * @returns Audit log listesi
 */
export async function getGymAuditLogs(
  gymId: string,
  filters?: {
    action?: string;
    resourceType?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }
) {
  try {
    // Güvenlik kontrolü
    const securityCheck = await checkManagerAccess(gymId);
    if (!securityCheck.success) {
      return { error: securityCheck.error };
    }

    const supabase = await createClient();

    let query = supabase
      .from("audit_logs")
      .select("*")
      .eq("gym_id", gymId)
      .order("created_at", { ascending: false });

    // Filtreler
    if (filters?.action) {
      query = query.eq("action", filters.action);
    }

    if (filters?.resourceType) {
      query = query.eq("resource_type", filters.resourceType);
    }

    if (filters?.status) {
      query = query.eq("status", filters.status);
    }

    if (filters?.startDate) {
      query = query.gte("created_at", filters.startDate);
    }

    if (filters?.endDate) {
      query = query.lte("created_at", filters.endDate);
    }

    // Limit
    const limit = filters?.limit || 50;
    query = query.limit(limit);

    const { data, error } = await query;

    if (error) {
      console.error("Audit logları alınırken hata:", error);
      return { error: error.message };
    }

    // Başarı audit log
    await logSuccess(
      "view_audit_logs",
      "system",
      `Audit logları görüntülendi: ${data?.length || 0} kayıt`,
      {
        gymId: gymId,
        metadata: {
          result_count: data?.length || 0,
          filters: filters,
        },
      }
    );

    return { success: true, data };
  } catch (error: any) {
    console.error("Audit logları alınırken hata:", error);
    return { error: "Audit logları alınırken bir hata oluştu." };
  }
}

/**
 * Manager için rapor verilerini getirir
 * @param filters Rapor filtreleri
 * @returns Rapor verileri
 */
export async function getManagerReports(
  filters: ReportFilters
): Promise<ApiResponse<ReportData>> {
  return await createAction(
    async (_, supabase, userId) => {
      // Güvenlik kontrolü
      const securityCheck = await checkManagerAccess();
      if (!securityCheck.success) {
        throw new Error(securityCheck.error || "Yetki hatası");
      }

      const managerGyms = securityCheck.managerGyms || [];
      const gymIds = managerGyms.map((gym: any) => gym.id);

      if (gymIds.length === 0) {
        return {
          totalRevenue: 0,
          totalMembers: 0,
          totalGyms: 0,
          monthlyGrowth: 0,
          revenueGrowth: 0,
          memberGrowth: 0,
          topPerformingGyms: [],
          monthlyData: [],
        };
      }

      // Tarih aralıklarını hesapla
      const now = new Date();
      let startDate: Date;
      let previousStartDate: Date;
      let previousEndDate: Date;

      switch (filters.period) {
        case "month":
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          previousStartDate = new Date(
            now.getFullYear(),
            now.getMonth() - 1,
            1
          );
          previousEndDate = new Date(now.getFullYear(), now.getMonth(), 0);
          break;
        case "quarter":
          const quarterStart = Math.floor(now.getMonth() / 3) * 3;
          startDate = new Date(now.getFullYear(), quarterStart, 1);
          previousStartDate = new Date(now.getFullYear(), quarterStart - 3, 1);
          previousEndDate = new Date(now.getFullYear(), quarterStart, 0);
          break;
        case "year":
          startDate = new Date(now.getFullYear(), 0, 1);
          previousStartDate = new Date(now.getFullYear() - 1, 0, 1);
          previousEndDate = new Date(now.getFullYear() - 1, 11, 31);
          break;
      }

      // Mevcut dönem verileri
      const [currentRevenue, currentMembers, previousRevenue, previousMembers] =
        await Promise.all([
          // Mevcut dönem geliri
          supabase
            .from("subscriptions")
            .select(
              `
            purchase_price,
            gym_package:gym_packages!inner(gym_id)
          `
            )
            .in("gym_package.gym_id", gymIds)
            .gte("start_date", startDate.toISOString())
            .lte("start_date", now.toISOString()),

          // Mevcut dönem üye sayısı
          supabase
            .from("memberships")
            .select("id", { count: "exact", head: true })
            .in("gym_id", gymIds)
            .eq("status", "active"),

          // Önceki dönem geliri
          supabase
            .from("subscriptions")
            .select(
              `
            purchase_price,
            gym_package:gym_packages!inner(gym_id)
          `
            )
            .in("gym_package.gym_id", gymIds)
            .gte("start_date", previousStartDate.toISOString())
            .lte("start_date", previousEndDate.toISOString()),

          // Önceki dönem üye sayısı (yaklaşık hesaplama)
          supabase
            .from("memberships")
            .select("id", { count: "exact", head: true })
            .in("gym_id", gymIds)
            .lte("created_at", previousEndDate.toISOString()),
        ]);

      // Gelir hesaplamaları
      const totalRevenue =
        currentRevenue.data?.reduce(
          (sum, sub) => sum + (sub.purchase_price || 0),
          0
        ) || 0;
      const totalPreviousRevenue =
        previousRevenue.data?.reduce(
          (sum, sub) => sum + (sub.purchase_price || 0),
          0
        ) || 0;
      const revenueGrowth =
        totalPreviousRevenue > 0
          ? ((totalRevenue - totalPreviousRevenue) / totalPreviousRevenue) * 100
          : 0;

      // Üye sayısı hesaplamaları
      const totalMembers = currentMembers.count || 0;
      const totalPreviousMembers = previousMembers.count || 0;
      const memberGrowth =
        totalPreviousMembers > 0
          ? ((totalMembers - totalPreviousMembers) / totalPreviousMembers) * 100
          : 0;

      // Salon bazında performans verileri
      const gymPerformancePromises = managerGyms.map(async (gym: any) => {
        const [gymRevenue, gymMembers] = await Promise.all([
          supabase
            .from("subscriptions")
            .select(
              `
              purchase_price,
              gym_package:gym_packages!inner(gym_id)
            `
            )
            .eq("gym_package.gym_id", gym.id)
            .gte("start_date", startDate.toISOString())
            .lte("start_date", now.toISOString()),

          supabase
            .from("memberships")
            .select("id", { count: "exact", head: true })
            .eq("gym_id", gym.id)
            .eq("status", "active"),
        ]);

        const revenue =
          gymRevenue.data?.reduce(
            (sum, sub) => sum + (sub.purchase_price || 0),
            0
          ) || 0;
        const members = gymMembers.count || 0;

        return {
          id: gym.id,
          name: gym.name,
          slug: gym.slug,
          revenue,
          members,
          growth: Math.random() * 20 - 5, // Geçici - gerçek growth hesaplaması için önceki dönem verisi gerekli
        };
      });

      const topPerformingGyms = await Promise.all(gymPerformancePromises);
      topPerformingGyms.sort((a, b) => b.revenue - a.revenue);

      // Aylık veriler (son 3 ay)
      const monthlyData = [];
      for (let i = 2; i >= 0; i--) {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

        const [monthRevenue, monthMembers] = await Promise.all([
          supabase
            .from("subscriptions")
            .select(
              `
              purchase_price,
              gym_package:gym_packages!inner(gym_id)
            `
            )
            .in("gym_package.gym_id", gymIds)
            .gte("start_date", monthStart.toISOString())
            .lte("start_date", monthEnd.toISOString()),

          supabase
            .from("memberships")
            .select("id", { count: "exact", head: true })
            .in("gym_id", gymIds)
            .lte("created_at", monthEnd.toISOString()),
        ]);

        const revenue =
          monthRevenue.data?.reduce(
            (sum, sub) => sum + (sub.purchase_price || 0),
            0
          ) || 0;
        const members = monthMembers.count || 0;

        monthlyData.push({
          month: monthStart.toLocaleDateString("tr-TR", { month: "long" }),
          revenue,
          members,
        });
      }

      const reportData: ReportData = {
        totalRevenue,
        totalMembers,
        totalGyms: managerGyms.length,
        monthlyGrowth: (revenueGrowth + memberGrowth) / 2, // Ortalama büyüme
        revenueGrowth,
        memberGrowth,
        topPerformingGyms,
        monthlyData,
      };

      return reportData;
    },
    { requireAuth: true }
  );
}
