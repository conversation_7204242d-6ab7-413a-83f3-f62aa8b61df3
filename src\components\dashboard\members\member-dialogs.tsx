"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus, Search, UserPlus } from "lucide-react";
import { toast } from "sonner";

import {
  createMemberByManager,
  inviteMemberToGym,
  searchUsers,
} from "@/app/actions/manager-actions";

// Form schema
const newUserSchema = z.object({
  email: z.string().email("Geçerli bir e-posta adresi giriniz"),
  name: z.string().min(2, "Ad en az 2 karakter olmalıdır"),
  surname: z.string().min(2, "Soyad en az 2 karakter olmalıdır"),
  gender: z.enum(["male", "female", "other", "prefer_not_to_say"], {
    required_error: "Lütfen bir cinsiyet seçin",
  }),
});

type NewUserFormValues = z.infer<typeof newUserSchema>;

interface MemberDialogsProps {
  gymId: string;
  isAddDialogOpen: boolean;
  onAddDialogChange: (open: boolean) => void;
  onMembersUpdate: () => void;
}

export function MemberDialogs({
  gymId,
  isAddDialogOpen,
  onAddDialogChange,
  onMembersUpdate,
}: MemberDialogsProps) {
  const [addMemberTab, setAddMemberTab] = useState("search");
  const [existingUserSearch, setExistingUserSearch] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form setup
  const newUserForm = useForm<NewUserFormValues>({
    resolver: zodResolver(newUserSchema),
    defaultValues: {
      email: "",
      name: "",
      surname: "",
      gender: "prefer_not_to_say",
    },
  });

  // Search existing users
  const searchExistingUsers = async () => {
    if (!existingUserSearch.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await searchUsers(existingUserSearch, []);
      if (response.success) {
        setSearchResults(response.data || []);
      } else {
        toast.error(response.error || "Kullanıcı aranırken bir hata oluştu");
      }
    } catch (error) {
      console.error("Search error:", error);
      toast.error("Kullanıcı aranırken bir hata oluştu");
    } finally {
      setIsSearching(false);
    }
  };

  // Create new user
  const handleAddNewUser = async (values: NewUserFormValues) => {
    setIsSubmitting(true);
    try {
      const response = await createMemberByManager({
        email: values.email,
        name: values.name,
        surname: values.surname,
        gender: values.gender,
        gymId: gymId,
      });

      if (response.success) {
        toast.success("Kullanıcı başarıyla oluşturuldu ve davet edildi");
        onMembersUpdate();
        resetDialog();
        onAddDialogChange(false);
      } else {
        toast.error(response.error || "Kullanıcı oluşturulamadı");
      }
    } catch (error) {
      console.error("Create user error:", error);
      toast.error("Kullanıcı oluşturulurken bir hata oluştu");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Invite existing user
  const inviteExistingUser = async (userId: string) => {
    setIsSubmitting(true);
    try {
      const response = await inviteMemberToGym(userId, gymId);
      if (response.success) {
        toast.success("Kullanıcı başarıyla davet edildi");
        onMembersUpdate();
        resetDialog();
        onAddDialogChange(false);
      } else {
        toast.error(response.error || "Kullanıcı davet edilemedi");
      }
    } catch (error) {
      console.error("Invite error:", error);
      toast.error("Kullanıcı davet edilirken bir hata oluştu");
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetDialog = () => {
    setAddMemberTab("search");
    setExistingUserSearch("");
    setSearchResults([]);
    newUserForm.reset();
  };

  const getUserInitials = (user: any) => {
    if (!user?.name) return "??";
    const nameParts = `${user.name} ${user.surname || ""}`.trim().split(/\s+/);
    return nameParts
      .map((p) => p[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getUserFullName = (user: any) => {
    if (!user) return "";
    return `${user.name || ""} ${user.surname || ""}`.trim() || user.email;
  };

  return (
    <Dialog
      open={isAddDialogOpen}
      onOpenChange={(open) => {
        onAddDialogChange(open);
        if (!open) resetDialog();
      }}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Üye Ekle</DialogTitle>
          <DialogDescription>
            Yeni üye ekleyin veya mevcut üyeleri arayarak davet edin
          </DialogDescription>
        </DialogHeader>

        <Tabs
          defaultValue="search"
          value={addMemberTab}
          onValueChange={setAddMemberTab}
          className="pt-2"
        >
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="search">Mevcut Kullanıcı Ara</TabsTrigger>
            <TabsTrigger value="create">Yeni Kullanıcı Oluştur</TabsTrigger>
          </TabsList>

          <TabsContent value="search" className="space-y-4 py-4">
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  placeholder="İsim, soyisim veya e-posta ara..."
                  value={existingUserSearch}
                  onChange={(e) => setExistingUserSearch(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && existingUserSearch.length >= 2) {
                      searchExistingUsers();
                    }
                  }}
                  className="flex-1"
                />
                <Button
                  onClick={searchExistingUsers}
                  disabled={isSearching || existingUserSearch.length < 2}
                >
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                En az 2 karakter girerek arama yapabilirsiniz
              </p>
            </div>

            <div className="space-y-2">
              {searchResults.length === 0 &&
                existingUserSearch.length >= 2 &&
                !isSearching && (
                  <p className="text-sm text-center text-muted-foreground py-4">
                    Arama kriterlerinize uygun kullanıcı bulunamadı
                  </p>
                )}

              {searchResults.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-3 border rounded-md"
                >
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage
                        src={user.profile_picture_url || ""}
                        alt={user.name || ""}
                      />
                      <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{getUserFullName(user)}</div>
                      <div className="text-sm text-muted-foreground">
                        {user.email}
                      </div>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => inviteExistingUser(user.id)}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <UserPlus className="h-4 w-4 mr-2" />
                    )}
                    Davet Et
                  </Button>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="create" className="py-4">
            <Form {...newUserForm}>
              <form
                onSubmit={newUserForm.handleSubmit(handleAddNewUser)}
                className="space-y-4"
              >
                <FormField
                  control={newUserForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>E-posta</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormDescription>Üyenin e-posta adresi</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={newUserForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ad</FormLabel>
                        <FormControl>
                          <Input placeholder="Ad" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={newUserForm.control}
                    name="surname"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Soyad</FormLabel>
                        <FormControl>
                          <Input placeholder="Soyad" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={newUserForm.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cinsiyet</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Cinsiyet seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="male">Erkek</SelectItem>
                          <SelectItem value="female">Kadın</SelectItem>
                          <SelectItem value="other">Diğer</SelectItem>
                          <SelectItem value="prefer_not_to_say">
                            Belirtmek İstemiyorum
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter className="pt-4">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Üye Oluştur
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
